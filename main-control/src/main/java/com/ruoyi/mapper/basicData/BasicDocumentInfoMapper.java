package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicDocumentInfo;
import com.ruoyi.utils.DocumentQueryParamVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface BasicDocumentInfoMapper extends BaseMapper<BasicDocumentInfo> {

    BasicDocumentInfo getDocumentInfoByCode(String transactionCode);

    /**
     * 增强查询单据信息（支持物料查询、供应商/客户名称查询等高级功能）
     * @param param 增强查询参数
     * @return 单据信息列表
     */
    List<BasicDocumentInfo> queryDocumentInfoEnhanced(@Param("param") DocumentQueryParamVO param);
}
