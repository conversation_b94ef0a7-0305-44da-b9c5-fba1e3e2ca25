package com.ruoyi.controller.basicData;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.basicData.*;
import com.ruoyi.service.basicData.*;
import com.ruoyi.service.document.DocumentDetailResponse;
import com.ruoyi.service.work.DocumentExecuteService;
import com.ruoyi.utils.GsonUtils;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.basicData.*;
import com.ruoyi.vo.document.DocumentInventoryVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import com.ruoyi.vo.webRequest.DeleteReq;
import com.ruoyi.vo.webResponse.dto.BasicMaterialInfoDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 基础数据—单据管理
 */
@RestController
@RequestMapping("/speedbot/basicData/documentInfo")
public class DocumentInfoController extends BaseController {
    protected final Logger logger = LoggerFactory.getLogger(DocumentInfoController.class);

    @Resource
    private BasicDocumentInfoService basicDocumentInfoService;

    @Resource
    private BasicDocumentDetailService basicDocumentDetailService;

    @Resource
    private DocumentInventoryDetailService documentInventoryDetailService;

    @Resource
    DocumentExecuteService documentExecuteService;

    /**
     * 查询单据
     */
    @PostMapping("/queryDocumentInfo")
    public TableDataInfo queryDocumentInfo(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<BasicDocumentInfo> list = this.basicDocumentInfoService.queryDocumentInfo(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     *
     * 单据新增
     */
    @PostMapping("/addDocumentInfo")
    @Log(title = "单据新增", businessType = BusinessType.INSERT)
    public ResponseResult addDocumentInfo(@RequestBody @Validated BasicDocumentInfoDto param ){
        logger.info("WEB单据新增接口：" + GsonUtils.toJsonString(param));
        return basicDocumentInfoService.addDocumentInfo(param);
    }

    /**
     * 单据更新
     */
    @PostMapping("/updateDocumentInfo")
    @Log(title = "单据更新", businessType = BusinessType.UPDATE)
    public ResponseResult updateDocumentInfo(@RequestBody @Validated BasicDocumentInfoDto param ){
        return basicDocumentInfoService.updateDocumentInfo(param);
    }

    /**
     * 单据删除
     */
    @Log(title = "单据删除", businessType = BusinessType.DELETE)
    @PostMapping("/delDocumentInfo")
    public ResponseResult delDocumentInfo(@RequestBody BatchIdsReq req){
        return this.basicDocumentInfoService.delDocumentInfo(req);
    }


    /**
     * 查询单据详情
     */
    @PostMapping("/queryDocumentDetail")
    public TableDataInfo queryDocumentDetail(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<DocumentDetailResponse> list = basicDocumentDetailService.queryDocumentDetail(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     *
     * 单据详情新增
     */
    @PostMapping("/addDocumentDetail")
    @Log(title = "单据详情新增", businessType = BusinessType.INSERT)
    public ResponseResult addDocumentDetail(@RequestBody @Validated BasicDocumentDetail param ){
        return basicDocumentDetailService.addDocumentDetail(param);
    }

    /**
     * 单据详情更新
     */
    @PostMapping("/updateDocumentDetail")
    @Log(title = "单据详情更新", businessType = BusinessType.UPDATE)
    public ResponseResult updateDocumentDetail(@RequestBody @Validated BasicDocumentDetail param ){
        return basicDocumentDetailService.updateDocumentDetail(param);
    }

    /**
     * 单据详情删除
     */
    @PostMapping("/deleteDocumentDetail")
    @Log(title = "单据详情删除", businessType = BusinessType.DELETE)
    public ResponseResult deleteDocumentDetail(@RequestBody BatchIdsReq req ){
        return basicDocumentDetailService.deleteDocumentDetail(req);
    }



    /**
     * 查询单据物料明细
     */
    @PostMapping("/queryDocumentInventoryDetail")
    public TableDataInfo queryDocumentInventoryDetail(@RequestBody QueryParamVO queryParamVO){
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<DocumentInventoryDetailDto> list = documentInventoryDetailService.queryDocumentInventoryDetail(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 单据出入库
     */
    @PostMapping("/documentInventoryInOut")
    @Log(title = "单据出入库", businessType = BusinessType.INSERT)
    public ResponseResult documentInventoryInOut(@RequestBody DocumentInventoryVo param) {
        return documentExecuteService.documentInventoryInOut(param);
    }


}
