package com.ruoyi.domain.basicData;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *单据明细详情表
 */
@Data
@TableName("basic_document_detail")
public class BasicDocumentDetail {

    /**
     * 主键编码
     */
    private String id;
    /**
     * 父表单据id
     */
    private String documentCode;

    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 全部数量
     */
    private Integer quantity;
    /**
     * 已完成数量
     */
    private Integer completedNum;
    /**
     * 本次数量
     */
    private Integer currentNum;
    /**
     * 来料数量
     */
    private Integer incomeNum;
    /**
     * 质检状态 1无需质检 2待质检 3质检完成
     */
    private Integer qualityStatus;
    /**
     * 任务状态 0未完成，1已完成
     */
    private Integer taskStatus;
}